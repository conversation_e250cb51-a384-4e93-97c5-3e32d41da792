<?php
/**
 * Service Orders Management API
 */

header('Content-Type: application/json');
session_start();

$base_path = dirname(dirname(dirname(dirname(__DIR__))));
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Core' . DIRECTORY_SEPARATOR . 'KMS_Config' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_config.php';
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Core' . DIRECTORY_SEPARATOR . 'KMS_Functions' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_functions.php';

// Check admin access
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || !isset($_SESSION["is_admin"]) || $_SESSION["is_admin"] !== true) {
    echo json_encode(['success' => false, 'message' => 'Admin access required']);
    exit;
}

$action = $_GET['action'] ?? $_POST['action'] ?? '';

switch ($action) {
    case 'admin_get_orders':
        getOrders();
        break;
    case 'update_order_status':
        updateOrderStatus();
        break;
    case 'update_order_price':
        updateOrderPrice();
        break;
    case 'get_order_details':
        getOrderDetails();
        break;
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
        break;
}

function getOrders() {
    global $link;
    
    try {
        // Check if service_orders table exists
        $check_table_sql = "SHOW TABLES LIKE 'service_orders'";
        $table_result = mysqli_query($link, $check_table_sql);
        
        if (mysqli_num_rows($table_result) == 0) {
            createServiceOrdersTable();
        }
        
        $sql = "SELECT so.*, u.username, u.email, u.first_name, u.last_name
                FROM service_orders so 
                LEFT JOIN users u ON so.user_id = u.id 
                ORDER BY so.created_at DESC";
        
        $result = mysqli_query($link, $sql);
        
        if (!$result) {
            throw new Exception('Database query failed: ' . mysqli_error($link));
        }
        
        $orders = [];
        while ($row = mysqli_fetch_assoc($result)) {
            // Decode JSON fields
            if ($row['order_items']) {
                $row['order_items'] = json_decode($row['order_items'], true);
            }
            $orders[] = $row;
        }
        
        echo json_encode([
            'success' => true,
            'orders' => $orders
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

function createServiceOrdersTable() {
    global $link;
    
    $sql = "
        CREATE TABLE IF NOT EXISTS service_orders (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            category ENUM('optimize', 'print') NOT NULL,
            order_items JSON NOT NULL,
            original_total DECIMAL(10,2) NOT NULL,
            final_total DECIMAL(10,2) NOT NULL,
            status ENUM('pending', 'processing', 'completed', 'cancelled') DEFAULT 'pending',
            payment_method ENUM('credit', 'paypal', 'stripe') DEFAULT 'credit',
            notes TEXT,
            admin_notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id)
        )
    ";
    
    if (!mysqli_query($link, $sql)) {
        throw new Exception('Failed to create service_orders table: ' . mysqli_error($link));
    }
}

function updateOrderStatus() {
    global $link;
    
    try {
        $order_id = (int)($_POST['order_id'] ?? 0);
        $status = $_POST['status'] ?? '';
        $admin_notes = sanitize_input($_POST['admin_notes'] ?? '');
        
        if ($order_id <= 0 || empty($status)) {
            throw new Exception('Invalid order ID or status');
        }
        
        $valid_statuses = ['pending', 'processing', 'completed', 'cancelled'];
        if (!in_array($status, $valid_statuses)) {
            throw new Exception('Invalid status');
        }
        
        $sql = "UPDATE service_orders SET status = ?, admin_notes = ?, updated_at = NOW() WHERE id = ?";
        $stmt = execute_query($link, $sql, "ssi", [$status, $admin_notes, $order_id]);
        
        if (!$stmt) {
            throw new Exception('Failed to update order status');
        }
        
        mysqli_stmt_close($stmt);
        
        echo json_encode([
            'success' => true,
            'message' => 'Order status updated successfully'
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

function updateOrderPrice() {
    global $link;
    
    try {
        $order_id = (int)($_POST['order_id'] ?? 0);
        $final_total = (float)($_POST['final_total'] ?? 0);
        $admin_notes = sanitize_input($_POST['admin_notes'] ?? '');
        
        if ($order_id <= 0 || $final_total < 0) {
            throw new Exception('Invalid order ID or price');
        }
        
        $sql = "UPDATE service_orders SET final_total = ?, admin_notes = ?, updated_at = NOW() WHERE id = ?";
        $stmt = execute_query($link, $sql, "dsi", [$final_total, $admin_notes, $order_id]);
        
        if (!$stmt) {
            throw new Exception('Failed to update order price');
        }
        
        mysqli_stmt_close($stmt);
        
        echo json_encode([
            'success' => true,
            'message' => 'Order price updated successfully'
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

function getOrderDetails() {
    global $link;
    
    try {
        $order_id = (int)($_GET['order_id'] ?? 0);
        
        if ($order_id <= 0) {
            throw new Exception('Invalid order ID');
        }
        
        $sql = "SELECT so.*, u.username, u.email, u.first_name, u.last_name
                FROM service_orders so 
                LEFT JOIN users u ON so.user_id = u.id 
                WHERE so.id = ?";
        
        $stmt = execute_query($link, $sql, "i", [$order_id]);
        
        if (!$stmt) {
            throw new Exception('Database query failed');
        }
        
        $result = mysqli_stmt_get_result($stmt);
        $order = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);
        
        if (!$order) {
            throw new Exception('Order not found');
        }
        
        // Decode JSON fields
        if ($order['order_items']) {
            $order['order_items'] = json_decode($order['order_items'], true);
        }
        
        echo json_encode([
            'success' => true,
            'order' => $order
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

close_db_connection($link);
?>
