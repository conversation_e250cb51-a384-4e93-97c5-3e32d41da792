<?php
/**
 * Admin Get Orders API
 */

header('Content-Type: application/json');
session_start();

$base_path = dirname(dirname(dirname(dirname(__DIR__))));
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Core' . DIRECTORY_SEPARATOR . 'KMS_Config' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_config.php';
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Core' . DIRECTORY_SEPARATOR . 'KMS_Functions' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_functions.php';

// Check admin access
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || !isset($_SESSION["is_admin"]) || $_SESSION["is_admin"] !== true) {
    echo json_encode(['success' => false, 'message' => 'Admin access required']);
    exit;
}

$action = $_GET['action'] ?? $_POST['action'] ?? 'get_pc_orders';

switch ($action) {
    case 'get_orders':
        getOrders();
        break;
    case 'get_pc_orders':
    default:
        getPCOrders();
        break;
}

function getOrders() {
    global $link;
    
    try {
        // Get service orders
        $sql = "SELECT so.*, u.username, u.email 
                FROM service_orders so 
                LEFT JOIN users u ON so.user_id = u.id 
                ORDER BY so.created_at DESC";
        
        $result = mysqli_query($link, $sql);
        
        if (!$result) {
            throw new Exception('Database query failed: ' . mysqli_error($link));
        }
        
        $orders = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $orders[] = $row;
        }
        
        echo json_encode([
            'success' => true,
            'orders' => $orders
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

function getPCOrders() {
    global $link;
    
    try {
        // Check if pc_orders table exists
        $check_table_sql = "SHOW TABLES LIKE 'pc_orders'";
        $table_result = mysqli_query($link, $check_table_sql);
        
        if (mysqli_num_rows($table_result) == 0) {
            // Create pc_orders table if it doesn't exist
            $create_table_sql = "
                CREATE TABLE pc_orders (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    order_type ENUM('simple', 'detailed', 'prebuilt') NOT NULL,
                    configuration JSON NOT NULL,
                    total_price DECIMAL(10,2) NOT NULL,
                    status ENUM('pending', 'processing', 'completed', 'cancelled') DEFAULT 'pending',
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            ";
            
            if (!mysqli_query($link, $create_table_sql)) {
                throw new Exception('Failed to create pc_orders table: ' . mysqli_error($link));
            }
        }
        
        // Get PC orders
        $sql = "SELECT po.*, u.username, u.email 
                FROM pc_orders po 
                LEFT JOIN users u ON po.user_id = u.id 
                ORDER BY po.created_at DESC";
        
        $result = mysqli_query($link, $sql);
        
        if (!$result) {
            throw new Exception('Database query failed: ' . mysqli_error($link));
        }
        
        $orders = [];
        while ($row = mysqli_fetch_assoc($result)) {
            // Decode JSON configuration
            if ($row['configuration']) {
                $row['configuration'] = json_decode($row['configuration'], true);
            }
            $orders[] = $row;
        }
        
        echo json_encode([
            'success' => true,
            'orders' => $orders
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

close_db_connection($link);
?>
