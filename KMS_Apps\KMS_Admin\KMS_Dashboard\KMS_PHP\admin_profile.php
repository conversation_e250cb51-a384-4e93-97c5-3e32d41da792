<?php
/**
 * Admin Profile Management API
 */

header('Content-Type: application/json');
session_start();

$base_path = dirname(dirname(dirname(dirname(__DIR__))));
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Core' . DIRECTORY_SEPARATOR . 'KMS_Config' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_config.php';
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Core' . DIRECTORY_SEPARATOR . 'KMS_Functions' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_functions.php';

// Check admin access
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || !isset($_SESSION["is_admin"]) || $_SESSION["is_admin"] !== true) {
    echo json_encode(['success' => false, 'message' => 'Admin access required']);
    exit;
}

$action = $_GET['action'] ?? $_POST['action'] ?? '';

switch ($action) {
    case 'get_profile':
        getProfile();
        break;
    case 'update_profile':
        updateProfile();
        break;
    case 'change_password':
        changePassword();
        break;
    case 'get_system_info':
        getSystemInfo();
        break;
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
        break;
}

function getProfile() {
    global $link;
    
    try {
        $admin_id = $_SESSION['id'];
        
        $sql = "SELECT id, username, email, first_name, last_name, nickname, created_at, last_login FROM users WHERE id = ? AND is_admin = 1";
        $stmt = execute_query($link, $sql, "i", [$admin_id]);
        
        if (!$stmt) {
            throw new Exception('Database query failed');
        }
        
        $result = mysqli_stmt_get_result($stmt);
        $profile = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);
        
        if (!$profile) {
            throw new Exception('Admin profile not found');
        }
        
        echo json_encode([
            'success' => true,
            'profile' => $profile
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

function updateProfile() {
    global $link;
    
    try {
        $admin_id = $_SESSION['id'];
        $first_name = sanitize_input($_POST['first_name'] ?? '');
        $last_name = sanitize_input($_POST['last_name'] ?? '');
        $nickname = sanitize_input($_POST['nickname'] ?? '');
        $email = sanitize_input($_POST['email'] ?? '');
        
        if (empty($email)) {
            throw new Exception('Email is required');
        }
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Invalid email format');
        }
        
        // Check if email is already taken by another user
        $check_sql = "SELECT id FROM users WHERE email = ? AND id != ?";
        $check_stmt = execute_query($link, $check_sql, "si", [$email, $admin_id]);
        
        if (!$check_stmt) {
            throw new Exception('Database error');
        }
        
        $check_result = mysqli_stmt_get_result($check_stmt);
        if (mysqli_num_rows($check_result) > 0) {
            mysqli_stmt_close($check_stmt);
            throw new Exception('Email is already taken');
        }
        mysqli_stmt_close($check_stmt);
        
        // Update profile
        $update_sql = "UPDATE users SET first_name = ?, last_name = ?, nickname = ?, email = ?, updated_at = NOW() WHERE id = ? AND is_admin = 1";
        $update_stmt = execute_query($link, $update_sql, "ssssi", [$first_name, $last_name, $nickname, $email, $admin_id]);
        
        if (!$update_stmt) {
            throw new Exception('Failed to update profile');
        }
        
        mysqli_stmt_close($update_stmt);
        
        echo json_encode([
            'success' => true,
            'message' => 'Profile updated successfully'
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

function changePassword() {
    global $link;
    
    try {
        $admin_id = $_SESSION['id'];
        $current_password = $_POST['current_password'] ?? '';
        $new_password = $_POST['new_password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        
        if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
            throw new Exception('All password fields are required');
        }
        
        if ($new_password !== $confirm_password) {
            throw new Exception('New passwords do not match');
        }
        
        if (strlen($new_password) < 6) {
            throw new Exception('New password must be at least 6 characters long');
        }
        
        // Verify current password
        $verify_sql = "SELECT password FROM users WHERE id = ? AND is_admin = 1";
        $verify_stmt = execute_query($link, $verify_sql, "i", [$admin_id]);
        
        if (!$verify_stmt) {
            throw new Exception('Database error');
        }
        
        $result = mysqli_stmt_get_result($verify_stmt);
        $user = mysqli_fetch_assoc($result);
        mysqli_stmt_close($verify_stmt);
        
        if (!$user || !password_verify($current_password, $user['password'])) {
            throw new Exception('Current password is incorrect');
        }
        
        // Update password
        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
        $update_sql = "UPDATE users SET password = ?, updated_at = NOW() WHERE id = ? AND is_admin = 1";
        $update_stmt = execute_query($link, $update_sql, "si", [$hashed_password, $admin_id]);
        
        if (!$update_stmt) {
            throw new Exception('Failed to update password');
        }
        
        mysqli_stmt_close($update_stmt);
        
        echo json_encode([
            'success' => true,
            'message' => 'Password changed successfully'
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

function getSystemInfo() {
    global $link;
    
    try {
        // Get system statistics
        $stats = [];
        
        // Total users
        $users_sql = "SELECT COUNT(*) as total FROM users WHERE is_admin = 0";
        $users_result = mysqli_query($link, $users_sql);
        $stats['total_users'] = mysqli_fetch_assoc($users_result)['total'];
        
        // Active users
        $active_sql = "SELECT COUNT(*) as active FROM users WHERE is_admin = 0 AND is_active = 1";
        $active_result = mysqli_query($link, $active_sql);
        $stats['active_users'] = mysqli_fetch_assoc($active_result)['active'];
        
        // Total orders (if table exists)
        $orders_check = mysqli_query($link, "SHOW TABLES LIKE 'service_orders'");
        if (mysqli_num_rows($orders_check) > 0) {
            $orders_sql = "SELECT COUNT(*) as total FROM service_orders";
            $orders_result = mysqli_query($link, $orders_sql);
            $stats['total_orders'] = mysqli_fetch_assoc($orders_result)['total'];
        } else {
            $stats['total_orders'] = 0;
        }
        
        // System info
        $stats['php_version'] = phpversion();
        $stats['server_software'] = $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown';
        $stats['mysql_version'] = mysqli_get_server_info($link);
        
        echo json_encode([
            'success' => true,
            'stats' => $stats
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

close_db_connection($link);
?>
