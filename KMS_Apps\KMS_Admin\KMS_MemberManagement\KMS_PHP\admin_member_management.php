<?php
/**
 * Admin Member Management API
 */

header('Content-Type: application/json');
session_start();

$base_path = dirname(dirname(dirname(dirname(__DIR__))));
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Core' . DIRECTORY_SEPARATOR . 'KMS_Config' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_config.php';
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Core' . DIRECTORY_SEPARATOR . 'KMS_Functions' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_functions.php';

// Check admin access
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || !isset($_SESSION["is_admin"]) || $_SESSION["is_admin"] !== true) {
    echo json_encode(['success' => false, 'message' => 'Admin access required']);
    exit;
}

$action = $_GET['action'] ?? $_POST['action'] ?? '';

switch ($action) {
    case 'get_members':
        getMembers();
        break;
    case 'get_member_details':
        getMemberDetails();
        break;
    case 'update_member':
        updateMember();
        break;
    case 'toggle_member_status':
        toggleMemberStatus();
        break;
    case 'delete_member':
        deleteMember();
        break;
    case 'get_member_stats':
        getMemberStats();
        break;
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
        break;
}

function getMembers() {
    global $link;
    
    try {
        $sql = "SELECT u.id, u.username, u.email, u.first_name, u.last_name, u.nickname,
                       u.phone_number, u.is_active, u.is_verified, u.created_at, u.last_login,
                       COALESCE(w.balance, 0) as balance,
                       COALESCE(w.total_deposited, 0) as total_deposited
                FROM users u 
                LEFT JOIN user_wallets w ON u.id = w.user_id 
                WHERE u.is_admin = 0 
                ORDER BY u.created_at DESC";
        
        $result = mysqli_query($link, $sql);
        
        if (!$result) {
            throw new Exception('Database query failed: ' . mysqli_error($link));
        }
        
        $members = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $members[] = $row;
        }
        
        echo json_encode([
            'success' => true,
            'members' => $members
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

function getMemberDetails() {
    global $link;
    
    try {
        $member_id = (int)($_GET['member_id'] ?? 0);
        
        if ($member_id <= 0) {
            throw new Exception('Invalid member ID');
        }
        
        $sql = "SELECT u.*, COALESCE(w.balance, 0) as balance, COALESCE(w.total_deposited, 0) as total_deposited
                FROM users u 
                LEFT JOIN user_wallets w ON u.id = w.user_id 
                WHERE u.id = ? AND u.is_admin = 0";
        
        $stmt = execute_query($link, $sql, "i", [$member_id]);
        
        if (!$stmt) {
            throw new Exception('Database query failed');
        }
        
        $result = mysqli_stmt_get_result($stmt);
        $member = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);
        
        if (!$member) {
            throw new Exception('Member not found');
        }
        
        echo json_encode([
            'success' => true,
            'member' => $member
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

function updateMember() {
    global $link;
    
    try {
        $member_id = (int)($_POST['member_id'] ?? 0);
        $first_name = sanitize_input($_POST['first_name'] ?? '');
        $last_name = sanitize_input($_POST['last_name'] ?? '');
        $nickname = sanitize_input($_POST['nickname'] ?? '');
        $email = sanitize_input($_POST['email'] ?? '');
        $phone_number = sanitize_input($_POST['phone_number'] ?? '');
        
        if ($member_id <= 0) {
            throw new Exception('Invalid member ID');
        }
        
        $sql = "UPDATE users SET first_name = ?, last_name = ?, nickname = ?, email = ?, phone_number = ?, updated_at = NOW() WHERE id = ? AND is_admin = 0";
        $stmt = execute_query($link, $sql, "sssssi", [$first_name, $last_name, $nickname, $email, $phone_number, $member_id]);
        
        if (!$stmt) {
            throw new Exception('Failed to update member');
        }
        
        mysqli_stmt_close($stmt);
        
        echo json_encode([
            'success' => true,
            'message' => 'Member updated successfully'
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

function toggleMemberStatus() {
    global $link;
    
    try {
        $member_id = (int)($_POST['member_id'] ?? 0);
        
        if ($member_id <= 0) {
            throw new Exception('Invalid member ID');
        }
        
        $sql = "UPDATE users SET is_active = NOT is_active, updated_at = NOW() WHERE id = ? AND is_admin = 0";
        $stmt = execute_query($link, $sql, "i", [$member_id]);
        
        if (!$stmt) {
            throw new Exception('Failed to toggle member status');
        }
        
        mysqli_stmt_close($stmt);
        
        echo json_encode([
            'success' => true,
            'message' => 'Member status updated successfully'
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

function deleteMember() {
    global $link;
    
    try {
        $member_id = (int)($_POST['member_id'] ?? 0);
        
        if ($member_id <= 0) {
            throw new Exception('Invalid member ID');
        }
        
        mysqli_begin_transaction($link);
        
        // Delete related records first
        $delete_wallet_sql = "DELETE FROM user_wallets WHERE user_id = ?";
        $delete_wallet_stmt = execute_query($link, $delete_wallet_sql, "i", [$member_id]);
        if ($delete_wallet_stmt) {
            mysqli_stmt_close($delete_wallet_stmt);
        }
        
        $delete_transactions_sql = "DELETE FROM credit_transactions WHERE user_id = ?";
        $delete_transactions_stmt = execute_query($link, $delete_transactions_sql, "i", [$member_id]);
        if ($delete_transactions_stmt) {
            mysqli_stmt_close($delete_transactions_stmt);
        }
        
        // Delete user
        $delete_user_sql = "DELETE FROM users WHERE id = ? AND is_admin = 0";
        $delete_user_stmt = execute_query($link, $delete_user_sql, "i", [$member_id]);
        
        if (!$delete_user_stmt) {
            throw new Exception('Failed to delete member');
        }
        
        mysqli_stmt_close($delete_user_stmt);
        mysqli_commit($link);
        
        echo json_encode([
            'success' => true,
            'message' => 'Member deleted successfully'
        ]);
        
    } catch (Exception $e) {
        mysqli_rollback($link);
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

function getMemberStats() {
    global $link;
    
    try {
        // Get total members
        $total_sql = "SELECT COUNT(*) as total FROM users WHERE is_admin = 0";
        $total_result = mysqli_query($link, $total_sql);
        $total_members = mysqli_fetch_assoc($total_result)['total'];
        
        // Get active members
        $active_sql = "SELECT COUNT(*) as active FROM users WHERE is_admin = 0 AND is_active = 1";
        $active_result = mysqli_query($link, $active_sql);
        $active_members = mysqli_fetch_assoc($active_result)['active'];
        
        // Get verified members
        $verified_sql = "SELECT COUNT(*) as verified FROM users WHERE is_admin = 0 AND is_verified = 1";
        $verified_result = mysqli_query($link, $verified_sql);
        $verified_members = mysqli_fetch_assoc($verified_result)['verified'];
        
        // Get new members this month
        $new_sql = "SELECT COUNT(*) as new_members FROM users WHERE is_admin = 0 AND created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)";
        $new_result = mysqli_query($link, $new_sql);
        $new_members = mysqli_fetch_assoc($new_result)['new_members'];
        
        echo json_encode([
            'success' => true,
            'stats' => [
                'total_members' => (int)$total_members,
                'active_members' => (int)$active_members,
                'verified_members' => (int)$verified_members,
                'new_members' => (int)$new_members
            ]
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

close_db_connection($link);
?>
