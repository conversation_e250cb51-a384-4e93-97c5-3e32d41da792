<?php
/**
 * Admin Credit Management API
 */

header('Content-Type: application/json');
session_start();

$base_path = dirname(dirname(dirname(dirname(__DIR__))));
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Core' . DIRECTORY_SEPARATOR . 'KMS_Config' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_config.php';
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Core' . DIRECTORY_SEPARATOR . 'KMS_Functions' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_functions.php';

// Check admin access
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || !isset($_SESSION["is_admin"]) || $_SESSION["is_admin"] !== true) {
    echo json_encode(['success' => false, 'message' => 'Admin access required']);
    exit;
}

$action = $_GET['action'] ?? $_POST['action'] ?? '';

switch ($action) {
    case 'get_system_stats':
        getSystemStats();
        break;
    case 'get_all_members':
        getAllMembers();
        break;
    case 'add_credit':
        addCredit();
        break;
    case 'get_transactions':
        getTransactions();
        break;
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
        break;
}

function getSystemStats() {
    global $link;
    
    try {
        // Get total members
        $members_sql = "SELECT COUNT(*) as total_members FROM users WHERE is_admin = 0";
        $members_result = mysqli_query($link, $members_sql);
        $total_members = mysqli_fetch_assoc($members_result)['total_members'];
        
        // Get total credit balance
        $balance_sql = "SELECT COALESCE(SUM(balance), 0) as total_balance FROM user_wallets";
        $balance_result = mysqli_query($link, $balance_sql);
        $total_balance = mysqli_fetch_assoc($balance_result)['total_balance'];
        
        // Get total transactions
        $transactions_sql = "SELECT COUNT(*) as total_transactions FROM credit_transactions";
        $transactions_result = mysqli_query($link, $transactions_sql);
        $total_transactions = mysqli_fetch_assoc($transactions_result)['total_transactions'];
        
        // Get pending withdrawals
        $withdrawals_sql = "SELECT COUNT(*) as pending_withdrawals FROM withdrawal_requests WHERE status = 'pending'";
        $withdrawals_result = mysqli_query($link, $withdrawals_sql);
        $pending_withdrawals = mysqli_fetch_assoc($withdrawals_result)['pending_withdrawals'] ?? 0;
        
        echo json_encode([
            'success' => true,
            'stats' => [
                'total_members' => (int)$total_members,
                'total_balance' => (float)$total_balance,
                'total_transactions' => (int)$total_transactions,
                'pending_withdrawals' => (int)$pending_withdrawals
            ]
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

function getAllMembers() {
    global $link;
    
    try {
        $sql = "SELECT u.id, u.username, u.email, u.first_name, u.last_name, 
                       COALESCE(w.balance, 0) as balance,
                       COALESCE(w.total_deposited, 0) as total_deposited,
                       u.created_at, u.is_active
                FROM users u 
                LEFT JOIN user_wallets w ON u.id = w.user_id 
                WHERE u.is_admin = 0 
                ORDER BY u.created_at DESC";
        
        $result = mysqli_query($link, $sql);
        
        if (!$result) {
            throw new Exception('Database query failed: ' . mysqli_error($link));
        }
        
        $members = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $members[] = $row;
        }
        
        echo json_encode([
            'success' => true,
            'members' => $members
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

function addCredit() {
    global $link;
    
    try {
        $user_id = (int)($_POST['user_id'] ?? 0);
        $amount = (float)($_POST['amount'] ?? 0);
        $description = sanitize_input($_POST['description'] ?? 'Admin credit adjustment');
        
        if ($user_id <= 0 || $amount <= 0) {
            throw new Exception('Invalid user ID or amount');
        }
        
        mysqli_begin_transaction($link);
        
        // Get current balance
        $balance_sql = "SELECT balance FROM user_wallets WHERE user_id = ?";
        $balance_stmt = execute_query($link, $balance_sql, "i", [$user_id]);
        
        if (!$balance_stmt) {
            throw new Exception('Failed to get current balance');
        }
        
        $balance_result = mysqli_stmt_get_result($balance_stmt);
        $current_balance = 0;
        
        if ($row = mysqli_fetch_assoc($balance_result)) {
            $current_balance = $row['balance'];
        } else {
            // Create wallet if it doesn't exist
            $create_wallet_sql = "INSERT INTO user_wallets (user_id, balance, total_deposited) VALUES (?, 0, 0)";
            $create_stmt = execute_query($link, $create_wallet_sql, "i", [$user_id]);
            if (!$create_stmt) {
                throw new Exception('Failed to create wallet');
            }
            mysqli_stmt_close($create_stmt);
        }
        mysqli_stmt_close($balance_stmt);
        
        // Update balance
        $new_balance = $current_balance + $amount;
        $update_sql = "UPDATE user_wallets SET balance = ?, total_deposited = total_deposited + ? WHERE user_id = ?";
        $update_stmt = execute_query($link, $update_sql, "ddi", [$new_balance, $amount, $user_id]);
        
        if (!$update_stmt) {
            throw new Exception('Failed to update balance');
        }
        mysqli_stmt_close($update_stmt);
        
        // Record transaction
        $transaction_id = 'ADMIN_' . time() . '_' . $user_id;
        $transaction_sql = "INSERT INTO credit_transactions (transaction_id, user_id, transaction_type, amount, balance_before, balance_after, status, description, reference_type, created_at) VALUES (?, ?, 'admin_gift', ?, ?, ?, 'completed', ?, 'admin', NOW())";
        $transaction_stmt = execute_query($link, $transaction_sql, "sidddss", [$transaction_id, $user_id, $amount, $current_balance, $new_balance, $description]);
        
        if (!$transaction_stmt) {
            throw new Exception('Failed to record transaction');
        }
        mysqli_stmt_close($transaction_stmt);
        
        mysqli_commit($link);
        
        echo json_encode([
            'success' => true,
            'message' => 'Credit added successfully',
            'new_balance' => $new_balance
        ]);
        
    } catch (Exception $e) {
        mysqli_rollback($link);
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

function getTransactions() {
    global $link;
    
    try {
        $sql = "SELECT ct.*, u.username 
                FROM credit_transactions ct 
                LEFT JOIN users u ON ct.user_id = u.id 
                ORDER BY ct.created_at DESC 
                LIMIT 100";
        
        $result = mysqli_query($link, $sql);
        
        if (!$result) {
            throw new Exception('Database query failed: ' . mysqli_error($link));
        }
        
        $transactions = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $transactions[] = $row;
        }
        
        echo json_encode([
            'success' => true,
            'transactions' => $transactions
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

close_db_connection($link);
?>
