<?php
/**
 * Service Prices Management API
 */

header('Content-Type: application/json');
session_start();

$base_path = dirname(dirname(dirname(dirname(__DIR__))));
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Core' . DIRECTORY_SEPARATOR . 'KMS_Config' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_config.php';
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Core' . DIRECTORY_SEPARATOR . 'KMS_Functions' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_functions.php';

// Check admin access
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || !isset($_SESSION["is_admin"]) || $_SESSION["is_admin"] !== true) {
    echo json_encode(['success' => false, 'message' => 'Admin access required']);
    exit;
}

$action = $_GET['action'] ?? $_POST['action'] ?? '';

switch ($action) {
    case 'get_prices':
        getPrices();
        break;
    case 'update_price':
        updatePrice();
        break;
    case 'add_service':
        addService();
        break;
    case 'delete_service':
        deleteService();
        break;
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
        break;
}

function getPrices() {
    global $link;
    
    try {
        $category = $_GET['category'] ?? 'all';
        $active_only = ($_GET['active_only'] ?? 'true') === 'true';
        
        // Check if service_prices table exists, create if not
        $check_table_sql = "SHOW TABLES LIKE 'service_prices'";
        $table_result = mysqli_query($link, $check_table_sql);
        
        if (mysqli_num_rows($table_result) == 0) {
            createServicePricesTable();
            insertDefaultServices();
        }
        
        $sql = "SELECT * FROM service_prices WHERE 1=1";
        $params = [];
        $types = "";
        
        if ($category !== 'all') {
            $sql .= " AND service_category = ?";
            $params[] = $category;
            $types .= "s";
        }
        
        if ($active_only) {
            $sql .= " AND is_active = 1";
        }
        
        $sql .= " ORDER BY service_category, sort_order, service_name";
        
        if (!empty($params)) {
            $stmt = execute_query($link, $sql, $types, $params);
            $result = mysqli_stmt_get_result($stmt);
        } else {
            $result = mysqli_query($link, $sql);
        }
        
        if (!$result) {
            throw new Exception('Database query failed: ' . mysqli_error($link));
        }
        
        $services = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $services[] = $row;
        }
        
        if (isset($stmt)) {
            mysqli_stmt_close($stmt);
        }
        
        echo json_encode([
            'success' => true,
            'services' => $services
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

function createServicePricesTable() {
    global $link;
    
    $sql = "
        CREATE TABLE IF NOT EXISTS service_prices (
            id INT AUTO_INCREMENT PRIMARY KEY,
            service_category ENUM('optimize', 'print') NOT NULL,
            service_type VARCHAR(100) NOT NULL DEFAULT 'general',
            item_name VARCHAR(200) NOT NULL,
            item_name_en VARCHAR(200) NOT NULL,
            item_name_zh VARCHAR(200) NOT NULL,
            base_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            unit VARCHAR(50) DEFAULT 'each',
            description TEXT,
            description_en TEXT,
            description_zh TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            sort_order INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_category (service_category),
            INDEX idx_type (service_type),
            INDEX idx_active (is_active),
            INDEX idx_sort (sort_order)
        )
    ";
    
    if (!mysqli_query($link, $sql)) {
        throw new Exception('Failed to create service_prices table: ' . mysqli_error($link));
    }
}

function insertDefaultServices() {
    global $link;
    
    $services = [
        // Optimize services
        ['optimize', 'photo_optimize', 'Photo Optimization', 'Photo Optimization', '照片優化', 3.00, 'each', 'Optimize your low quality photo to the best quality', 'Optimize your low quality photo to the best quality', '將您的低質量照片優化到最佳質量', 1],
        ['optimize', 'photo_watermark', 'Photo Watermark Removal', 'Photo Watermark Removal', '照片去水印', 3.00, 'each', 'Remove watermark from photo', 'Remove watermark from photo', '去除照片水印', 2],
        ['optimize', 'video_watermark', 'Video Watermark Removal', 'Video Watermark Removal', '視頻去水印', 5.00, 'per minute', 'Remove watermark from video', 'Remove watermark from video', '去除視頻水印', 3],

        // Print services
        ['print', 'paper_32lb_black', '32 lb Premium Paper - Black', '32 lb Premium Paper - Black', '32磅高級紙 - 黑白', 0.50, 'per page', 'Letter size 32 lb premium paper in black', 'Letter size 32 lb premium paper in black', '信紙尺寸32磅高級紙黑白印刷', 1],
        ['print', 'paper_32lb_color', '32 lb Premium Paper - Color', '32 lb Premium Paper - Color', '32磅高級紙 - 彩色', 1.00, 'per page', 'Letter size 32 lb premium paper in color', 'Letter size 32 lb premium paper in color', '信紙尺寸32磅高級紙彩色印刷', 2],
        ['print', 'large_format', 'Large Format Print', 'Large Format Print', '大幅面列印', 10.00, 'per print', 'Large format poster printing', 'Large format poster printing', '大幅面海報印刷', 3],
    ];
    
    $sql = "INSERT INTO service_prices (service_category, service_type, item_name, item_name_en, item_name_zh, base_price, unit, description, description_en, description_zh, sort_order) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    foreach ($services as $service) {
        $stmt = execute_query($link, $sql, "sssssdssssi", $service);
        if ($stmt) {
            mysqli_stmt_close($stmt);
        }
    }
}

function updatePrice() {
    global $link;
    
    try {
        $id = (int)($_POST['id'] ?? 0);
        $current_price = (float)($_POST['current_price'] ?? 0);
        
        if ($id <= 0 || $current_price < 0) {
            throw new Exception('Invalid service ID or price');
        }
        
        $sql = "UPDATE service_prices SET current_price = ?, updated_at = NOW() WHERE id = ?";
        $stmt = execute_query($link, $sql, "di", [$current_price, $id]);
        
        if (!$stmt) {
            throw new Exception('Failed to update price');
        }
        
        mysqli_stmt_close($stmt);
        
        echo json_encode([
            'success' => true,
            'message' => 'Price updated successfully'
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

function addService() {
    global $link;
    
    try {
        $category = $_POST['category'] ?? '';
        $service_name = sanitize_input($_POST['service_name'] ?? '');
        $description = sanitize_input($_POST['description'] ?? '');
        $base_price = (float)($_POST['base_price'] ?? 0);
        $current_price = (float)($_POST['current_price'] ?? $base_price);
        $unit = sanitize_input($_POST['unit'] ?? 'per item');
        
        if (empty($category) || empty($service_name) || $base_price < 0) {
            throw new Exception('Invalid service data');
        }
        
        $sql = "INSERT INTO service_prices (service_category, service_type, item_name, item_name_en, item_name_zh, base_price, unit, description, description_en, description_zh, is_active, sort_order) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, 0)";
        $stmt = execute_query($link, $sql, "sssssdssss", [$category, 'general', $service_name, $service_name, $service_name, $base_price, $unit, $description, $description, $description]);
        
        if (!$stmt) {
            throw new Exception('Failed to add service');
        }
        
        mysqli_stmt_close($stmt);
        
        echo json_encode([
            'success' => true,
            'message' => 'Service added successfully'
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

function deleteService() {
    global $link;
    
    try {
        $id = (int)($_POST['id'] ?? 0);
        
        if ($id <= 0) {
            throw new Exception('Invalid service ID');
        }
        
        $sql = "DELETE FROM service_prices WHERE id = ?";
        $stmt = execute_query($link, $sql, "i", [$id]);
        
        if (!$stmt) {
            throw new Exception('Failed to delete service');
        }
        
        mysqli_stmt_close($stmt);
        
        echo json_encode([
            'success' => true,
            'message' => 'Service deleted successfully'
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

close_db_connection($link);
?>
