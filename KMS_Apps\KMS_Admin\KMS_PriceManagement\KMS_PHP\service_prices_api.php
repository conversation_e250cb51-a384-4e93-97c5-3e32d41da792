<?php
/**
 * Service Prices Management API
 */

header('Content-Type: application/json');
session_start();

$base_path = dirname(dirname(dirname(dirname(__DIR__))));
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Core' . DIRECTORY_SEPARATOR . 'KMS_Config' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_config.php';
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Core' . DIRECTORY_SEPARATOR . 'KMS_Functions' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_functions.php';

// Check admin access
if (!isset($_SESSION["loggedin"]) || $_SESSION["loggedin"] !== true || !isset($_SESSION["is_admin"]) || $_SESSION["is_admin"] !== true) {
    echo json_encode(['success' => false, 'message' => 'Admin access required']);
    exit;
}

$action = $_GET['action'] ?? $_POST['action'] ?? '';

switch ($action) {
    case 'get_prices':
        getPrices();
        break;
    case 'update_price':
        updatePrice();
        break;
    case 'add_service':
        addService();
        break;
    case 'delete_service':
        deleteService();
        break;
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
        break;
}

function getPrices() {
    global $link;
    
    try {
        $category = $_GET['category'] ?? 'all';
        $active_only = ($_GET['active_only'] ?? 'true') === 'true';
        
        // Check if service_prices table exists, create if not
        $check_table_sql = "SHOW TABLES LIKE 'service_prices'";
        $table_result = mysqli_query($link, $check_table_sql);
        
        if (mysqli_num_rows($table_result) == 0) {
            createServicePricesTable();
            insertDefaultServices();
        }
        
        $sql = "SELECT * FROM service_prices WHERE 1=1";
        $params = [];
        $types = "";
        
        if ($category !== 'all') {
            $sql .= " AND category = ?";
            $params[] = $category;
            $types .= "s";
        }
        
        if ($active_only) {
            $sql .= " AND is_active = 1";
        }
        
        $sql .= " ORDER BY category, sort_order, service_name";
        
        if (!empty($params)) {
            $stmt = execute_query($link, $sql, $types, $params);
            $result = mysqli_stmt_get_result($stmt);
        } else {
            $result = mysqli_query($link, $sql);
        }
        
        if (!$result) {
            throw new Exception('Database query failed: ' . mysqli_error($link));
        }
        
        $services = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $services[] = $row;
        }
        
        if (isset($stmt)) {
            mysqli_stmt_close($stmt);
        }
        
        echo json_encode([
            'success' => true,
            'services' => $services
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

function createServicePricesTable() {
    global $link;
    
    $sql = "
        CREATE TABLE IF NOT EXISTS service_prices (
            id INT AUTO_INCREMENT PRIMARY KEY,
            category ENUM('optimize', 'print') NOT NULL,
            service_name VARCHAR(200) NOT NULL,
            service_name_en VARCHAR(200) NOT NULL,
            service_name_zh VARCHAR(200) NOT NULL,
            description TEXT,
            base_price DECIMAL(10,2) NOT NULL,
            current_price DECIMAL(10,2) NOT NULL,
            unit VARCHAR(50) DEFAULT 'per item',
            is_active BOOLEAN DEFAULT TRUE,
            sort_order INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ";
    
    if (!mysqli_query($link, $sql)) {
        throw new Exception('Failed to create service_prices table: ' . mysqli_error($link));
    }
}

function insertDefaultServices() {
    global $link;
    
    $services = [
        // Optimize services
        ['optimize', 'Photo Optimization', 'Photo Optimization', '照片優化', 'Professional photo enhancement and optimization', 5.00, 5.00, 'per photo', 1, 1],
        ['optimize', 'Video Optimization', 'Video Optimization', '影片優化', 'Video compression and quality enhancement', 15.00, 15.00, 'per minute', 1, 2],
        ['optimize', 'Batch Photo Processing', 'Batch Photo Processing', '批量照片處理', 'Process multiple photos at once', 3.00, 3.00, 'per photo', 1, 3],
        
        // Print services
        ['print', 'Standard Print', 'Standard Print', '標準列印', 'High-quality standard printing', 0.50, 0.50, 'per page', 1, 1],
        ['print', 'Premium Print', 'Premium Print', '高級列印', 'Premium quality printing with special paper', 1.00, 1.00, 'per page', 1, 2],
        ['print', 'Large Format Print', 'Large Format Print', '大幅面列印', 'Large format poster printing', 10.00, 10.00, 'per print', 1, 3],
    ];
    
    $sql = "INSERT INTO service_prices (category, service_name, service_name_en, service_name_zh, description, base_price, current_price, unit, is_active, sort_order) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    foreach ($services as $service) {
        $stmt = execute_query($link, $sql, "sssssddsii", $service);
        if ($stmt) {
            mysqli_stmt_close($stmt);
        }
    }
}

function updatePrice() {
    global $link;
    
    try {
        $id = (int)($_POST['id'] ?? 0);
        $current_price = (float)($_POST['current_price'] ?? 0);
        
        if ($id <= 0 || $current_price < 0) {
            throw new Exception('Invalid service ID or price');
        }
        
        $sql = "UPDATE service_prices SET current_price = ?, updated_at = NOW() WHERE id = ?";
        $stmt = execute_query($link, $sql, "di", [$current_price, $id]);
        
        if (!$stmt) {
            throw new Exception('Failed to update price');
        }
        
        mysqli_stmt_close($stmt);
        
        echo json_encode([
            'success' => true,
            'message' => 'Price updated successfully'
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

function addService() {
    global $link;
    
    try {
        $category = $_POST['category'] ?? '';
        $service_name = sanitize_input($_POST['service_name'] ?? '');
        $description = sanitize_input($_POST['description'] ?? '');
        $base_price = (float)($_POST['base_price'] ?? 0);
        $current_price = (float)($_POST['current_price'] ?? $base_price);
        $unit = sanitize_input($_POST['unit'] ?? 'per item');
        
        if (empty($category) || empty($service_name) || $base_price < 0) {
            throw new Exception('Invalid service data');
        }
        
        $sql = "INSERT INTO service_prices (category, service_name, service_name_en, service_name_zh, description, base_price, current_price, unit, is_active, sort_order) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, 0)";
        $stmt = execute_query($link, $sql, "sssssdds", [$category, $service_name, $service_name, $service_name, $description, $base_price, $current_price, $unit]);
        
        if (!$stmt) {
            throw new Exception('Failed to add service');
        }
        
        mysqli_stmt_close($stmt);
        
        echo json_encode([
            'success' => true,
            'message' => 'Service added successfully'
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

function deleteService() {
    global $link;
    
    try {
        $id = (int)($_POST['id'] ?? 0);
        
        if ($id <= 0) {
            throw new Exception('Invalid service ID');
        }
        
        $sql = "DELETE FROM service_prices WHERE id = ?";
        $stmt = execute_query($link, $sql, "i", [$id]);
        
        if (!$stmt) {
            throw new Exception('Failed to delete service');
        }
        
        mysqli_stmt_close($stmt);
        
        echo json_encode([
            'success' => true,
            'message' => 'Service deleted successfully'
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

close_db_connection($link);
?>
