<?php
/**
 * Setup PC Components Data
 */

require_once 'KMS_Apps/KMS_Core/KMS_Config/KMS_PHP/KMS_config.php';

echo "<h2>Setting up PC Components...</h2>";

// Read and execute the SQL file
$sql_content = file_get_contents('SQL/insert_pc_components.sql');

// Split by semicolon to get individual queries
$queries = explode(';', $sql_content);

$success_count = 0;
$error_count = 0;

foreach ($queries as $query) {
    $query = trim($query);
    if (empty($query) || strpos($query, '--') === 0) {
        continue;
    }
    
    if (mysqli_query($link, $query)) {
        $success_count++;
        echo "<p style='color: green;'>✓ Query executed successfully</p>";
    } else {
        $error_count++;
        echo "<p style='color: red;'>✗ Error: " . mysqli_error($link) . "</p>";
        echo "<p style='color: red;'>Query: " . htmlspecialchars($query) . "</p>";
    }
}

echo "<h3>Summary:</h3>";
echo "<p>Successful queries: $success_count</p>";
echo "<p>Failed queries: $error_count</p>";

// Check if data was inserted
$check_categories = mysqli_query($link, "SELECT COUNT(*) as count FROM pc_categories");
$categories_count = mysqli_fetch_assoc($check_categories)['count'];

$check_components = mysqli_query($link, "SELECT COUNT(*) as count FROM pc_components");
$components_count = mysqli_fetch_assoc($check_components)['count'];

echo "<h3>Data Check:</h3>";
echo "<p>PC Categories: $categories_count</p>";
echo "<p>PC Components: $components_count</p>";

if ($categories_count > 0 && $components_count > 0) {
    echo "<h3 style='color: green;'>✓ PC Components setup completed successfully!</h3>";
} else {
    echo "<h3 style='color: red;'>✗ PC Components setup failed!</h3>";
}

close_db_connection($link);
?>
