/* Admin Order Management Styles */
body {
    font-family: Arial, sans-serif;
    background-color: #a48f19;
    color: white;
    margin: 0;
    padding: 20px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 3px;
}
::-webkit-scrollbar-track {
    background: rgba(0,0,0,0.1);
}
::-webkit-scrollbar-thumb {
    background: #05c3b6;
    border-radius: 3px;
}
::-webkit-scrollbar-thumb:hover {
    background: #04a89d;
}

.container {
    max-width: 1400px;
    margin: auto;
    background-color: rgb(5, 195, 182);
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgb(0, 0, 0);
}

h1, h2, h3 {
    color: #ffffff;
    text-align: center;
}

/* Navigation */
.nav-controls {
    text-align: center;
    margin-bottom: 30px;
}

.nav-controls button {
    padding: 10px 20px;
    margin: 0 5px;
    border: 2px solid rgb(253, 202, 0);
    border-radius: 10px;
    background-color: rgb(253, 202, 0);
    color: #ffffff;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.nav-controls button:hover {
    background-color: rgb(255, 215, 0);
    transform: translateY(-2px);
}

/* Tables */
.table-container {
    background-color: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
}

table {
    width: 100%;
    border-collapse: collapse;
    background-color: rgba(255, 255, 255, 0.1);
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

th {
    background-color: rgba(0, 0, 0, 0.3);
    font-weight: bold;
    color: #00ffff;
}

tr:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Order status styles */
.status-pending {
    color: #ffc107;
    font-weight: bold;
}

.status-processing {
    color: #17a2b8;
    font-weight: bold;
}

.status-completed {
    color: #28a745;
    font-weight: bold;
}

.status-cancelled {
    color: #dc3545;
    font-weight: bold;
}

/* Buttons */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    margin: 2px;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-warning {
    background-color: #ffc107;
    color: black;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-info {
    background-color: #17a2b8;
    color: white;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* Forms */
.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: #00ffff;
    font-weight: bold;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 5px;
    background-color: rgba(0, 0, 0, 0.3);
    color: white;
    font-size: 14px;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #00ffff;
    box-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: #2b9869;
    margin: 5% auto;
    padding: 20px;
    border-radius: 10px;
    width: 80%;
    max-width: 800px;
    position: relative;
    max-height: 80vh;
    overflow-y: auto;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    right: 15px;
    top: 10px;
}

.close:hover {
    color: white;
}

/* Order details */
.order-details {
    background-color: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
}

.order-items {
    background-color: rgba(0, 0, 0, 0.2);
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
}

.order-item {
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.order-item:last-child {
    border-bottom: none;
}

/* Statistics */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    border-left: 5px solid #00ffff;
}

.stat-value {
    font-size: 2em;
    font-weight: bold;
    color: #00ffff;
}

.stat-label {
    margin-top: 10px;
    color: white;
}

/* Search and filters */
.search-filters {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.search-filters input,
.search-filters select {
    padding: 10px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 5px;
    background-color: rgba(0, 0, 0, 0.3);
    color: white;
}

/* Price display */
.price-display {
    color: #00ff00;
    font-weight: bold;
    font-size: 1.1em;
}

.price-original {
    text-decoration: line-through;
    color: #ccc;
    margin-right: 10px;
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    table {
        font-size: 12px;
    }
    
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
    
    .search-filters {
        flex-direction: column;
    }
}
