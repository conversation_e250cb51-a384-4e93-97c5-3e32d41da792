// PC Builder Functions
let pcConfiguration = {};
let simpleConfig = {};
let detailedConfig = {};
let availableComponents = {};

function initPCBuilder() {
    loadComponentCategories();
    loadPrebuiltConfigs();
    checkAdminRestrictions();
}

function loadComponentCategories() {
    fetch(`KMS_PCBuilder/KMS_PHP/KMS_pc_components_api.php?action=get_components_by_category&PHPSESSID=${SESSION_ID}`, {
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            availableComponents = data.components_by_category;

            // Load components for detailed mode
            loadDetailedModeComponents();
        } else {
            console.error('Error loading components:', data.message);
        }
    })
    .catch(error => console.error('Error loading categories:', error));
}

function loadDetailedModeComponents() {
    // Map category names to container IDs
    const categoryMapping = {
        'CPU': 'cpu',
        'GPU': 'gpu',
        'RAM': 'ram',
        'Storage': 'storage'
    };

    // Load components for each category
    Object.entries(availableComponents).forEach(([categoryName, components]) => {
        const containerId = categoryMapping[categoryName];
        if (!containerId) return;

        const container = document.getElementById(`${containerId}-options`);
        if (!container) return;

        let html = '';

        components.forEach(component => {
            const componentData = {
                id: component.id,
                name: component.component_name,
                specs: component.description || '',
                price: parseFloat(component.current_price)
            };

            html += `
                <div class="component-option" onclick="selectDetailedComponent('${containerId}', ${component.id}, ${JSON.stringify(componentData).replace(/"/g, '&quot;')}, this)">
                    <div class="component-name">${component.component_name}</div>
                    <div class="component-specs">${component.description || ''}</div>
                    <div class="component-price">$${parseFloat(component.current_price).toFixed(2)}</div>
                </div>
            `;
        });

        container.innerHTML = html;
    });
}

function loadPrebuiltConfigs() {
    fetch(`pc_components_api.php?action=get_prebuilt_configs&active_only=1&PHPSESSID=${SESSION_ID}`, {
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.prebuiltConfigs = data.configs;
        }
    })
    .catch(error => console.error('Error loading prebuilt configs:', error));
}

function checkAdminRestrictions() {
    // This would typically check with the server for admin-set restrictions
    // For now, we'll assume no restrictions
    return { mode: 'all', restrictions: [] };
}

function selectPCMode(mode) {
    // Remove previous selection
    document.querySelectorAll('.mode-option').forEach(option => {
        option.style.border = '2px solid transparent';
    });

    // Highlight selected mode
    event.target.style.border = '2px solid #00ffff';

    // Hide all config areas
    document.querySelectorAll('.config-area').forEach(area => {
        area.style.display = 'none';
    });

    // Show selected config area
    document.getElementById(`${mode}-mode-config`).style.display = 'block';

    // Load content for the selected mode
    loadModeContent(mode);

    // Update global configuration
    pcConfiguration.mode = mode;
}

function loadModeContent(mode) {
    switch(mode) {
        case 'simple':
            loadSimpleModeContent();
            break;
        case 'detailed':
            loadDetailedModeContent();
            break;
        case 'prebuilt':
            loadPrebuiltModeContent();
            break;
    }
}

function loadSimpleModeContent() {
    const container = document.querySelector('#simple-mode-config .simple-config-grid');
    container.innerHTML = `
        <div class="simple-step" id="step-color">
            <h4>1. Choose Case Color</h4>
            <div class="simple-options">
                <div class="simple-option" onclick="selectSimpleOption('color', 'black', this)">
                    <div class="option-preview" style="background: #333;"></div>
                    <div class="option-name">Black</div>
                </div>
                <div class="simple-option" onclick="selectSimpleOption('color', 'white', this)">
                    <div class="option-preview" style="background: #fff; border: 1px solid #ccc;"></div>
                    <div class="option-name">White</div>
                </div>
                <div class="simple-option" onclick="selectSimpleOption('color', 'red', this)">
                    <div class="option-preview" style="background: #e74c3c;"></div>
                    <div class="option-name">Red</div>
                </div>
                <div class="simple-option" onclick="selectSimpleOption('color', 'blue', this)">
                    <div class="option-preview" style="background: #3498db;"></div>
                    <div class="option-name">Blue</div>
                </div>
            </div>
        </div>

        <div class="simple-step disabled" id="step-size">
            <h4>2. Choose Case Size</h4>
            <div class="simple-options">
                <div class="simple-option" onclick="selectSimpleOption('size', 'small', this)">
                    <div class="option-icon">📦</div>
                    <div class="option-name">Small</div>
                    <div class="option-desc">Mini-ITX</div>
                    <div class="option-price">+$200</div>
                </div>
                <div class="simple-option" onclick="selectSimpleOption('size', 'medium', this)">
                    <div class="option-icon">📦📦</div>
                    <div class="option-name">Medium</div>
                    <div class="option-desc">Micro-ATX</div>
                    <div class="option-price">+$200</div>
                </div>
                <div class="simple-option" onclick="selectSimpleOption('size', 'large', this)">
                    <div class="option-icon">📦📦📦</div>
                    <div class="option-name">Large</div>
                    <div class="option-desc">ATX Full Tower</div>
                    <div class="option-price">+$300</div>
                </div>
            </div>
        </div>

        <div class="simple-step disabled" id="step-use">
            <h4>3. Primary Use</h4>
            <div class="simple-options">
                <div class="simple-option" onclick="selectSimpleOption('use', 'gaming', this)">
                    <div class="option-icon">🎮</div>
                    <div class="option-name">Gaming</div>
                    <div class="option-desc">High-end graphics</div>
                </div>
                <div class="simple-option" onclick="selectSimpleOption('use', 'work', this)">
                    <div class="option-icon">💼</div>
                    <div class="option-name">Work</div>
                    <div class="option-desc">Productivity focused</div>
                </div>
                <div class="simple-option" onclick="selectSimpleOption('use', 'creative', this)">
                    <div class="option-icon">🎨</div>
                    <div class="option-name">Creative</div>
                    <div class="option-desc">Content creation</div>
                </div>
                <div class="simple-option" onclick="selectSimpleOption('use', 'general', this)">
                    <div class="option-icon">🖥️</div>
                    <div class="option-name">General</div>
                    <div class="option-desc">Everyday computing</div>
                </div>
            </div>
        </div>

        <div class="simple-step disabled" id="step-tier">
            <h4>4. Performance Tier</h4>
            <div class="simple-options">
                <div class="simple-option" onclick="selectSimpleOption('tier', 'budget', this)">
                    <div class="option-icon">💰</div>
                    <div class="option-name">Budget</div>
                    <div class="option-desc">Essential performance</div>
                </div>
                <div class="simple-option" onclick="selectSimpleOption('tier', 'mid', this)">
                    <div class="option-icon">⚡</div>
                    <div class="option-name">Mid-Range</div>
                    <div class="option-desc">Balanced performance</div>
                </div>
                <div class="simple-option" onclick="selectSimpleOption('tier', 'high', this)">
                    <div class="option-icon">🚀</div>
                    <div class="option-name">High-End</div>
                    <div class="option-desc">Premium performance</div>
                </div>
                <div class="simple-option" onclick="selectSimpleOption('tier', 'extreme', this)">
                    <div class="option-icon">👑</div>
                    <div class="option-name">Extreme</div>
                    <div class="option-desc">Ultimate performance</div>
                </div>
            </div>
        </div>

        <div class="simple-step disabled" id="step-budget">
            <h4>5. Budget Range</h4>
            <div class="simple-options">
                <div class="simple-option" onclick="selectSimpleOption('budget', '1000', this)">
                    <div class="option-name">$1,000</div>
                    <div class="option-desc">Entry Level</div>
                </div>
                <div class="simple-option" onclick="selectSimpleOption('budget', '1500', this)">
                    <div class="option-name">$1,500</div>
                    <div class="option-desc">Mid Range</div>
                </div>
                <div class="simple-option" onclick="selectSimpleOption('budget', '2500', this)">
                    <div class="option-name">$2,500</div>
                    <div class="option-desc">High End</div>
                </div>
                <div class="simple-option" onclick="selectSimpleOption('budget', '4000', this)">
                    <div class="option-name">$4,000+</div>
                    <div class="option-desc">Premium</div>
                </div>
                <div class="simple-option" onclick="selectSimpleOption('budget', 'custom', this)">
                    <div class="option-name">Custom</div>
                    <div class="option-desc">Set your own</div>
                </div>
            </div>
            <div id="custom-budget" style="display: none; margin-top: 15px;">
                <input type="number" id="custom-budget-value" placeholder="Enter budget" 
                       style="width: 100%; padding: 10px; border-radius: 5px; background: #333; color: white; border: 1px solid #555;"
                       onchange="updateCustomBudget()">
            </div>
        </div>

        <div class="simple-step disabled" id="step-system">
            <h4>6. System Package</h4>
            <div class="simple-options">
                <div class="simple-option" onclick="selectSimpleOption('system', 'windows_home', this)">
                    <div class="option-icon">🪟</div>
                    <div class="option-name">Windows 11 Home</div>
                    <div class="option-desc">+$139</div>
                </div>
                <div class="simple-option" onclick="selectSimpleOption('system', 'windows_pro', this)">
                    <div class="option-icon">🪟</div>
                    <div class="option-name">Windows 11 Pro</div>
                    <div class="option-desc">+$199</div>
                </div>
                <div class="simple-option" onclick="selectSimpleOption('system', 'none', this)">
                    <div class="option-icon">💾</div>
                    <div class="option-name">No OS</div>
                    <div class="option-desc">Bring your own</div>
                </div>
            </div>
            <div style="margin-top: 20px; padding: 15px; background: rgba(255,193,7,0.1); border-radius: 10px; border-left: 4px solid #ffc107;">
                <div style="font-size: 20px; font-weight: bold; color: #ffc107; margin-bottom: 10px;">
                    💼 System Service Package - $100 (Required)
                </div>
                <div style="color: #ccc; font-size: 14px; line-height: 1.4;">
                    Includes professional assembly, testing, optimization, and setup.<br>
                    <strong>Note:</strong> Shipping costs and installation services are additional.
                </div>
            </div>
        </div>
    `;

    // Initialize simple config
    simpleConfig = {
        color: null,
        size: null,
        use: null,
        tier: null,
        budget: null,
        system: null
    };
}

// Removed duplicate selectSimpleOption function - using the one at line 354

function enableNextStep(currentStep) {
    const stepOrder = ['color', 'size', 'use', 'tier', 'budget', 'system'];
    const currentIndex = stepOrder.indexOf(currentStep);

    // Special handling for budget step (it's part of tier step now)
    if (currentStep === 'budget') {
        // Enable system step after budget selection
        const systemStep = document.getElementById('step-system');
        if (systemStep) {
            systemStep.classList.remove('disabled');
            systemStep.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
        return;
    }

    if (currentIndex < stepOrder.length - 1) {
        const nextStep = stepOrder[currentIndex + 1];
        // Skip budget step since it's now part of tier step
        const actualNextStep = nextStep === 'budget' ? 'system' : nextStep;
        const nextStepElement = document.getElementById(`step-${actualNextStep}`);
        if (nextStepElement) {
            nextStepElement.classList.remove('disabled');
            // Scroll to next step
            nextStepElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }
}

// Simple Mode Step Selection
function selectSimpleOption(step, value, element) {
    // Remove previous selection in this step or budget options
    if (step === 'budget') {
        const budgetOptions = document.getElementById('budget-options');
        budgetOptions.querySelectorAll('.option-btn').forEach(btn => btn.classList.remove('selected'));
    } else {
        const stepContainer = element.closest('.step-container');
        stepContainer.querySelectorAll('.option-btn').forEach(btn => btn.classList.remove('selected'));
    }

    // Select current option
    element.classList.add('selected');

    // Store selection
    simpleConfig[step] = value;

    // Special handling for tier selection - show budget options
    if (step === 'tier') {
        const budgetOptions = document.getElementById('budget-options');
        budgetOptions.style.display = 'block';
        // Don't enable next step yet, wait for budget selection
        updateSimpleConfig();
        return;
    }

    // Handle custom budget input
    if (step === 'budget' && value === 'custom') {
        const customInput = document.getElementById('custom-budget-value');
        customInput.style.display = 'block';
        customInput.focus();
        return; // Don't enable next step until custom value is entered
    } else if (step === 'budget' && value !== 'custom') {
        const customInput = document.getElementById('custom-budget-value');
        customInput.style.display = 'none';
    }

    // Enable next step
    enableNextStep(step);

    // Update configuration
    updateSimpleConfig();
}

function updateCustomBudget() {
    const customValue = document.getElementById('custom-budget-value').value;
    if (customValue && customValue >= 3500 && customValue <= 9999) {
        simpleConfig.budget = customValue;
        enableNextStep('budget');
        updateSimpleConfig();
    }
}

function updateSimpleConfig() {
    pcConfiguration = {
        mode: 'simple',
        config: simpleConfig,
        estimatedPrice: calculateSimplePrice()
    };

    updateConfigurationSummary();
}

// Detailed Mode Component Selection
function selectDetailedComponent(category, componentId, componentData, element) {
    // Remove previous selection in this category
    const categorySection = element.closest('.category-section');
    categorySection.querySelectorAll('.component-option').forEach(btn => btn.classList.remove('selected'));

    // Select current component
    element.classList.add('selected');

    // Store selection
    detailedConfig[category] = {
        id: componentId,
        name: componentData.name,
        specs: componentData.specs,
        price: componentData.price
    };

    // Update selected components display
    updateSelectedComponentsList();

    // Update configuration
    updateDetailedConfig();
}

function updateSelectedComponentsList() {
    const listContainer = document.getElementById('selected-components-list');

    if (Object.keys(detailedConfig).length === 0) {
        listContainer.innerHTML = '<div class="no-selection">No components selected yet</div>';
        document.getElementById('detailed-total').textContent = '$0.00';
        return;
    }

    let html = '';
    let total = 0;

    Object.entries(detailedConfig).forEach(([category, component]) => {
        html += `
            <div class="component-item">
                <div class="component-item-info">
                    <div class="component-item-name">${component.name}</div>
                    <div class="component-item-specs">${component.specs}</div>
                </div>
                <div class="component-item-price">$${component.price.toFixed(2)}</div>
            </div>
        `;
        total += component.price;
    });

    listContainer.innerHTML = html;
    document.getElementById('detailed-total').textContent = `$${total.toFixed(2)}`;
}

function updateDetailedConfig() {
    pcConfiguration = {
        mode: 'detailed',
        config: detailedConfig,
        estimatedPrice: calculateDetailedPrice()
    };

    updateConfigurationSummary();
}

function calculateDetailedPrice() {
    let total = 0;
    Object.values(detailedConfig).forEach(component => {
        total += component.price;
    });
    return total;
}

function calculateSimplePrice() {
    let basePrice = 0;
    
    // Add case price based on size
    if (simpleConfig.size === 'small' || simpleConfig.size === 'medium') {
        basePrice += 200;
    } else if (simpleConfig.size === 'large') {
        basePrice += 300;
    }
    
    // Add Windows price
    if (simpleConfig.system === 'windows_home') {
        basePrice += 139;
    } else if (simpleConfig.system === 'windows_pro') {
        basePrice += 199;
    }
    
    // Add system service package (mandatory)
    basePrice += 100;
    
    // Add estimated component cost based on budget
    if (simpleConfig.budget && simpleConfig.budget !== 'custom') {
        basePrice += parseInt(simpleConfig.budget);
    } else if (simpleConfig.budget === 'custom' && simpleConfig.customBudget) {
        basePrice += parseInt(simpleConfig.customBudget);
    }
    
    return basePrice;
}

function updateConfigurationSummary() {
    const summaryContent = document.getElementById('summary-content');
    const estimatedTotal = document.getElementById('estimated-total');
    const configSummary = document.getElementById('config-summary');

    if (!pcConfiguration.mode) {
        configSummary.style.display = 'none';
        return;
    }

    let summaryHtml = '';
    let totalPrice = 0;

    switch(pcConfiguration.mode) {
        case 'simple':
            summaryHtml = generateSimpleSummary();
            totalPrice = pcConfiguration.estimatedPrice || 0;
            break;
        case 'detailed':
            summaryHtml = generateDetailedSummary();
            totalPrice = pcConfiguration.estimatedPrice || 0;
            break;
        case 'prebuilt':
            summaryHtml = generatePrebuiltSummary();
            totalPrice = pcConfiguration.estimatedPrice || 0;
            break;
    }

    summaryContent.innerHTML = summaryHtml;
    estimatedTotal.textContent = '$' + totalPrice.toFixed(2);
    configSummary.style.display = 'block';
}

function generateSimpleSummary() {
    const config = simpleConfig;
    let html = '<div class="summary-grid">';
    
    if (config.color) html += `<div class="summary-item"><strong>Color:</strong> ${config.color}</div>`;
    if (config.size) html += `<div class="summary-item"><strong>Size:</strong> ${config.size}</div>`;
    if (config.use) html += `<div class="summary-item"><strong>Use:</strong> ${config.use}</div>`;
    if (config.tier) html += `<div class="summary-item"><strong>Tier:</strong> ${config.tier}</div>`;
    if (config.budget) html += `<div class="summary-item"><strong>Budget:</strong> $${config.budget}</div>`;
    if (config.system) html += `<div class="summary-item"><strong>OS:</strong> ${config.system.replace('_', ' ')}</div>`;
    
    html += '</div>';
    return html;
}

function requestPCQuote() {
    if (!pcConfiguration.mode) {
        showCustomAlert('error', 'Configuration Required', 'Please select a configuration mode first.');
        return;
    }

    const formData = new FormData();
    formData.append('action', 'request_pc_quote');
    formData.append('configuration', JSON.stringify(pcConfiguration));

    fetch('pc_components_api.php', {
        method: 'POST',
        body: formData,
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showCustomAlert('success', 'Quote Requested', 'Your PC quote request has been submitted successfully! We will contact you soon with a detailed quote.');
            // Reset configuration
            pcConfiguration = {};
            document.getElementById('config-summary').style.display = 'none';
        } else {
            showCustomAlert('error', 'Request Failed', 'Error submitting quote request: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showCustomAlert('error', 'Connection Error', 'Error submitting quote request');
    });
}
